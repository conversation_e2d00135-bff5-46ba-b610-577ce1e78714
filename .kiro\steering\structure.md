# Project Structure

## Root Level Organization
```
├── src/                    # Source code
├── public/                 # Static assets
├── docs/                   # Documentation and screenshots
├── .kiro/                  # Kiro AI assistant configuration
├── .env.example            # Environment variables template
├── Dockerfile              # Container configuration
├── ecosystem.config.cjs    # PM2 process management
└── package.json            # Dependencies and scripts
```

## Source Code Structure (`src/`)

### Application Layer
- **`src/app/`**: Next.js App Router pages and layouts
  - `layout.tsx`: Root layout with providers
  - `page.tsx`: Main application page
  - `globals.css`: Global styles and CSS variables
  - `_component/`: Page-specific components

### Component Architecture
- **`src/components/`**: Reusable React components
  - `ui/`: Shadcn/ui components (buttons, dialogs, etc.)
  - `image-editor/`: Specialized image editing components
  - Individual feature components (modals, bars, viewers)

### Core Modules
- **`src/lib/`**: Core utilities and configurations
  - `api.ts`: API client and request handling
  - `utils.ts`: Common utility functions (cn, etc.)

- **`src/stores/`**: Zustand state management
  - `index.ts`: Store configuration
  - `slices/`: Individual store slices

- **`src/types/`**: TypeScript type definitions
- **`src/constants/`**: Application constants and configurations

### Internationalization
- **`src/locales/`**: Multi-language support
  - `en.ts`, `ja.ts`, `zh.ts`: Language files
  - `index.ts`: Locale configuration

### Assets & Utilities
- **`src/icons/`**: SVG icons (converted to React components)
- **`src/utils/`**: Specialized utilities
  - `Image.ts`: Image processing helpers
  - `Storage.ts`: Local storage management
  - `System.ts`: System-level utilities

## Naming Conventions
- **Components**: PascalCase (e.g., `ImageViewer.tsx`)
- **Files**: kebab-case for multi-word files (e.g., `image-editor.tsx`)
- **Directories**: kebab-case or camelCase consistently
- **Icons**: kebab-case SVG files, converted to PascalCase components

## Import Patterns
- Use `@/` alias for src imports: `import { cn } from "@/lib/utils"`
- Relative imports for same-directory files
- SVG imports: `import Icon from "@/icons/icon-name.svg"`

## Component Organization
- Keep related components in feature-specific folders
- Separate UI primitives in `components/ui/`
- Complex features get their own subdirectories
- Co-locate types and utilities with components when specific to that feature