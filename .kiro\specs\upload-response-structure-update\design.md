# Design Document

## Overview

本设计文档描述了如何更新项目中的上传接口处理逻辑，以适配新的API响应结构。主要涉及修改 `src/lib/api.ts` 文件中的 `uploadImage` 函数，更新响应结构解析逻辑和鉴权方式。

## Architecture

### Current Architecture
```
uploadImage(file) -> FormData -> POST request -> response.data.data.url
```

### New Architecture
```
uploadImage(file) -> FormData -> POST request with Bearer auth -> response.data.url
```

## Components and Interfaces

### 1. Upload Function Interface

**Current Interface:**
```typescript
export async function uploadImage(file: File): Promise<string | undefined>
```

**Updated Interface:**
```typescript
export async function uploadImage(file: File): Promise<string | undefined>
```
接口保持不变，但内部实现需要更新。

### 2. API Response Types

**Current Response Structure:**
```typescript
{
  data: {
    data: {
      url: string
    }
  }
}
```

**New Response Structure:**
```typescript
{
  success: boolean;
  message: string;
  data: {
    key: string;
    url: string;
    filename: string;
    size: number;
    mime_type: string;
    uploaded_at: string;
  };
  timestamp: string;
}
```

### 3. Error Response Structure

**New Error Response:**
```typescript
{
  success: false;
  message: string;
  timestamp: string;
}
```

## Data Models

### Upload Response Type Definition

```typescript
interface UploadResponse {
  success: boolean;
  message: string;
  data: {
    key: string;
    url: string;
    filename: string;
    size: number;
    mime_type: string;
    uploaded_at: string;
  };
  timestamp: string;
}

interface UploadErrorResponse {
  success: false;
  message: string;
  timestamp: string;
}
```

## Error Handling

### 1. HTTP Error Handling
- 检查 `response.ok` 状态
- 对于非200状态码，解析错误响应并抛出异常

### 2. API Error Handling
- 检查响应中的 `success` 字段
- 当 `success: false` 时，从 `message` 字段获取错误信息并抛出异常

### 3. Response Validation
- 验证响应结构是否符合预期
- 确保 `data.url` 字段存在且为有效字符串

## Implementation Details

### 1. Authentication Update
- 从环境变量 `NEXT_PUBLIC_API_KEY` 获取API密钥
- 在请求头中添加 `Authorization: Bearer ${apiKey}`

### 2. Response Processing Update
- 更新响应解析逻辑，从 `data.url` 而不是 `data.data.url` 获取URL
- 添加 `success` 字段检查

### 3. Error Handling Enhancement
- 改进错误处理逻辑，支持新的错误响应格式
- 保持现有的错误处理行为（静默失败）

## Testing Strategy

### 1. Unit Tests
- 测试成功上传场景，验证URL正确提取
- 测试API错误响应场景，验证错误正确处理
- 测试HTTP错误场景，验证异常正确抛出
- 测试鉴权头正确设置

### 2. Integration Tests
- 测试与实际API的集成
- 验证不同文件类型的上传
- 验证错误场景的端到端处理

### 3. Regression Tests
- 确保所有依赖 `uploadImage` 函数的功能仍能正常工作
- 测试图片处理流程的完整性

## Migration Strategy

### 1. Code Changes
- 更新 `uploadImage` 函数实现
- 添加类型定义（可选，用于更好的类型安全）

### 2. Environment Configuration
- 确保 `NEXT_PUBLIC_API_KEY` 环境变量正确配置
- 更新 `.env.example` 文件说明（如需要）

### 3. Deployment Considerations
- 确保新的API密钥在部署环境中正确配置
- 验证新API端点的可用性

## Backward Compatibility

由于这是API端点的完全替换，不需要考虑向后兼容性。但需要确保：
- 所有调用 `uploadImage` 的代码无需修改
- 函数的返回值格式保持一致
- 错误处理行为保持一致