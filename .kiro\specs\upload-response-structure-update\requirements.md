# Requirements Document

## Introduction

本功能旨在更新项目中上传接口的响应结构处理逻辑，以适应新的API响应格式。当前系统期望上传接口返回 `data.data.url` 格式的响应，但新的API接口返回的是 `{ success: boolean, data: { url: string, ... } }` 格式的响应结构。同时需要更新鉴权方式，使用 `NEXT_PUBLIC_API_KEY` 进行Bearer token认证。

## Requirements

### Requirement 1

**User Story:** 作为开发者，我希望系统能够正确处理新的上传接口响应结构，以便图片上传功能能够正常工作。

#### Acceptance Criteria

1. WHEN 用户上传图片文件 THEN 系统 SHALL 能够从新的响应结构 `{ success: true, data: { url: string } }` 中正确提取图片URL
2. WHEN 上传接口返回 `success: true` THEN 系统 SHALL 从 `data.url` 字段获取图片URL
3. WHEN 上传接口返回 `success: false` THEN 系统 SHALL 能够正确处理错误响应并提供适当的错误信息
4. WHEN 发送上传请求时 THEN 系统 SHALL 使用 `Bearer ${NEXT_PUBLIC_API_KEY}` 格式进行鉴权

### Requirement 2

**User Story:** 作为系统维护者，我希望上传功能能够正确处理新API的响应格式和错误情况，以便系统能够稳定运行。

#### Acceptance Criteria

1. WHEN 接收到新格式响应 `{ success: true, data: { url, key, filename, size, mime_type, uploaded_at } }` 时 THEN 系统 SHALL 能够正确提取URL
2. WHEN 接收到错误响应 `{ success: false, message: string }` 时 THEN 系统 SHALL 能够正确处理错误并抛出异常
3. IF HTTP状态码不是200时 THEN 系统 SHALL 能够正确处理HTTP错误

### Requirement 3

**User Story:** 作为用户，我希望图片上传功能在API更新后仍能正常工作，以便我能够继续使用所有依赖上传功能的图片处理工具。

#### Acceptance Criteria

1. WHEN 使用任何需要上传图片的功能时 THEN 系统 SHALL 能够成功上传并获取图片URL
2. WHEN 上传完成后 THEN 系统 SHALL 能够在后续的图片处理流程中正确使用上传的图片
3. WHEN 上传过程中出现错误时 THEN 系统 SHALL 向用户显示清晰的错误提示

### Requirement 4

**User Story:** 作为开发者，我希望能够轻松配置和测试不同的响应结构格式，以便在开发和调试过程中验证功能的正确性。

#### Acceptance Criteria

1. WHEN 需要调试上传功能时 THEN 系统 SHALL 提供清晰的日志信息显示响应结构
2. WHEN 响应结构发生变化时 THEN 系统 SHALL 能够通过最小的代码修改来适配新格式
3. IF 需要支持多种响应格式时 THEN 系统 SHALL 具有可扩展的结构来处理不同格式