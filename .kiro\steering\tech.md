# Technology Stack

## Core Framework
- **Next.js 14**: React framework with App Router
- **TypeScript**: Strict typing enabled
- **React 18**: UI library with modern hooks

## Styling & UI
- **Tailwind CSS**: Utility-first CSS framework with custom design system
- **Shadcn/ui**: Component library (New York style)
- **Radix UI**: Headless UI primitives for complex components
- **CSS Variables**: Custom color system with HSL values
- **Tailwind Animate**: Animation utilities

## State Management & Data
- **Zustand**: Lightweight state management
- **TanStack Query**: Server state management and caching
- **React Context**: For localization and theme

## Image Processing & Canvas
- **Konva & React-Konva**: 2D canvas library for image manipulation
- **Sharp**: Server-side image processing
- **React Mobile Cropper**: Mobile-friendly image cropping
- **React Compare Slider**: Before/after image comparisons
- **React Zoom Pan Pinch**: Image zoom and pan functionality

## Development Tools
- **ESLint**: Code linting with Next.js config
- **SVGR**: SVG to React component conversion
- **Webpack**: Custom configuration for SVG handling

## Deployment
- **Docker**: Multi-stage build with Alpine Linux
- **PM2**: Process management (ecosystem.config.cjs)
- **Standalone Output**: Next.js standalone build for containers

## Common Commands

### Development
```bash
npm run dev          # Start development server on port 3000
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Docker Deployment
```bash
docker build -t image_toolbox .
docker run -p 3000:3000 image_toolbox
```

## Environment Configuration
- **NEXT_PUBLIC_API_KEY**: 302.AI API key
- **NEXT_PUBLIC_FETCH_API_URL**: API endpoint (https://api.302.ai)
- **NEXT_PUBLIC_UPLOAD_API_URL**: Upload endpoint