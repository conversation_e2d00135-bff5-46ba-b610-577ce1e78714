# Implementation Plan

- [x] 1. 添加类型定义


  - 在 `src/lib/api.ts` 文件中添加新的上传响应类型定义
  - 定义 `UploadResponse` 和 `UploadErrorResponse` 接口
  - _Requirements: 1.1, 2.1_

- [x] 2. 更新 uploadImage 函数的鉴权逻辑


  - 从环境变量 `NEXT_PUBLIC_API_KEY` 获取API密钥
  - 在请求头中添加 `Authorization: Bearer ${apiKey}` 鉴权
  - _Requirements: 1.4_

- [x] 3. 更新响应处理逻辑


  - 修改响应解析逻辑，从 `data.url` 而不是 `data.data.url` 获取URL
  - 添加 `success` 字段检查，确保上传成功
  - _Requirements: 1.1, 1.2, 2.1_

- [x] 4. 增强错误处理逻辑


  - 更新错误处理以支持新的错误响应格式 `{ success: false, message: string }`
  - 保持现有的静默失败行为
  - 改进HTTP错误状态码的处理
  - _Requirements: 1.3, 2.2, 2.3_

- [ ] 5. 验证和测试更新后的上传功能


  - 测试成功上传场景，确保URL正确提取
  - 测试错误场景，确保错误正确处理
  - 验证所有依赖上传功能的图片处理工具仍能正常工作
  - _Requirements: 3.1, 3.2, 3.3_